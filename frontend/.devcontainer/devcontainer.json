{
  "name": "CampGrid Frontend Dev",
  "dockerComposeFile": "../../docker-compose.yml",
  "service": "frontend",
  "workspaceFolder": "/frontend",
  "customizations": {
    "vscode": {
      "settings": {
        "terminal.integrated.defaultProfile.linux": "bash",
        "typescript.preferences.quoteStyle": "single",
        "javascript.preferences.quoteStyle": "single"
      },
      "extensions": [
        "dbaeumer.vscode-eslint",
        "esbenp.prettier-vscode",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-typescript-next"
      ]
    }
  }//,
  //"postCreateCommand": "npm install"
  //"forwardPorts": [5173]

}
