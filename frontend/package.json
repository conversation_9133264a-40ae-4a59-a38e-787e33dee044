{"name": "campgrid-frontend", "version": "1.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mantine/core": "8.1.3", "@mantine/form": "^8.2.1", "@mantine/hooks": "8.1.3", "@mui/material": "^7.2.0", "@tabler/icons-react": "^3.34.1", "@turf/turf": "^6.0.0", "axios": "^1.4.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-leaflet-draw": "^0.20.6", "react-router-dom": "^6.4.0", "react-transition-group": "^4.4.5"}, "devDependencies": {"@types/leaflet": "^1.9.19", "@types/leaflet-draw": "^1.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.0.0", "postcss": "^8.5.6", "postcss-preset-mantine": "^1.18.0", "postcss-simple-vars": "^7.0.1", "typescript": "^4.0.0", "vite": "^4.0.0"}}