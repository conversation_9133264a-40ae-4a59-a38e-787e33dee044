import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [react()],
  server: {
    host: true,
    watch: {
      usePolling: true,
    },
    port: 5173,
    strictPort: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://**************:8000',
        changeOrigin: true,
        secure: false,
  },
  css: {
    modules: {
      scopeBehaviour: 'local', // default is 'local'
    },
    postcss: './postcss.config.js', // optional if PostCSS config exists
  },
});
