import React from 'react';
import { useNavigate } from 'react-router-dom';
import { HeaderMenu } from '../Components/HeaderMenu';
import { Button, Center, Stack } from '@mantine/core';

function ProtectedRoute({ role, children }: { role: string; children: React.ReactNode }) {
  const token = localStorage.getItem("token");

  const navigate = useNavigate();

  if (!token) {
    return (
      <div>
        <HeaderMenu />
        <Stack justify='center' align='center'>
          <Center><h3>Sie sind derzeit nicht angemeldet</h3></Center>
          <Button onClick={() => navigate("/login")}>Anmelden</Button>
        </Stack>
      </div>
    );
  }

  const payload = JSON.parse(atob(token.split(".")[1]));
  if (payload.role !== role) {
    return (
      <div>
        <HeaderMenu />
        <Center><h3>Sie haben für diese Seite keine Berechtigung</h3></Center>
      </div>
    );
  }

  return <>{children}</>;
}

export default ProtectedRoute;
