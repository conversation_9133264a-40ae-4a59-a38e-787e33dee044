import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useParams } from 'react-router-dom';



function Campsite({  }: { }) {
  const { campsite_id } = useParams();
  const [campsites, setCampsites] = useState<{ id: string; email: string; name: string }[]>([]);

  const API_URL = import.meta.env.VITE_API_URL;

  useEffect(() => {
      // Fetch campsites from the backend
      axios.get(`${API_URL}/api/campsite/${campsite_id}`)
          .then((response) => {
              console.log('Fetched campsites:', response.data); // Debugging log
              setCampsites(response.data);
          })
          .catch((error) => {
              console.error('Error fetching campsites:', error);
          });
  }, []);
  

  return (
    <div>
      <h1>Campsite</h1>
      <ul>
        {campsites.map(campsite => (
                    <li><a href={`/campsites/${campsite.id}`}>{campsite.name} - Operator: {campsite.email}</a></li>
                ))}
      </ul>
    </div>
  );
}

export default Campsite;
