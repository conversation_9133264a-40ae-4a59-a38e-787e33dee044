import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { Button, Group, PasswordInput, Stack, TextInput } from '@mantine/core';

import classes from '../css/RegisterUser.module.css';

const RegisterUser = () => {
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [shake, setShake] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async () => {
    try {
      const response = await axios.post('http://localhost:8000/api/v1/auth/signup', {
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
      });
      alert(response.data.message);
      navigate('/user/Dashboard');
    } catch (error) {
      console.error('Error registering user:', error);
      alert('Failed to register user.' + error);
      triggerShake
    }
  };

  const handleEmailInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handleUsernameInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value);
  };

  const handlePasswordInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  const triggerShake = () => {
    setShake(true);
    setTimeout(() => setShake(false), 2500); // remove class after animation
  };
  

  return (
    <Stack gap={'lg'}>
      <Stack gap='sm' className={shake ? classes.shake : ''}>
        <Group align='space-between' grow>
          <TextInput required value={firstName} onChange={(e) => setFirstName(e.target.value)} placeholder="Vorname" />
          <TextInput required value={lastName} onChange={(e) => setLastName(e.target.value)} placeholder="Nachname" />
        </Group>
        <TextInput required value={email} onChange={handleEmailInput} placeholder="Email" />
        <TextInput required value={username} onChange={handleUsernameInput} placeholder="Benutzername" />

        <PasswordInput required value={password} onChange={handlePasswordInput} placeholder="Passwort" />
        <PasswordInput required value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} placeholder="Passwort bestätigen" />
      </Stack>
      <Stack gap='xs' >
        <Button onClick={handleRegister} >Registrieren</Button>
      </Stack>
    </Stack>
  );
};

export default RegisterUser;
