import { useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useM<PERSON>, <PERSON>ygon, Popup,FeatureGroup } from 'react-leaflet';
import { EditControl } from 'react-leaflet-draw';
import { forwardRef } from 'react';
import { FaEdit } from 'react-icons/fa';
import Button from '@mui/material/Button';

import axios from 'axios';
import L from 'leaflet';

import * as turf from '@turf/turf';
import { polygon } from 'leaflet';

function FlyToAddress({ position, flyToRequested, setFlyToRequested }: {
  position: [number, number],
  flyToRequested: boolean,
  setFlyToRequested: (val: boolean) => void
}) {
  const map = useMap();

  if (flyToRequested) {
    map.setView(position, 17);
    setFlyToRequested(false); // prevent repeated jumps
  }

  return null;
}


export default function CreateCampground() {
  const [address, setAddress] = useState('');
  const [position, setPosition] = useState<[number, number] | null>(null);
  const [polygons, setPolygons] = useState<[number, number][][]>([]);
  const [stellplaetze, setStellplaetze] = useState<Stellplatz[]>([]);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [flyToRequested, setFlyToRequested] = useState(false);



  type Stellplatz = {
    coordinates: [number, number][];
    platznummer: string;
    groesse: number; // in m²
    beschreibung: string;
  };


  const drawnItemsRef = useRef<L.FeatureGroup>(null);

  const handleSearch = async () => {
    const res = await axios.get(`https://nominatim.openstreetmap.org/search`, {
      params: {
        q: address,
        format: 'json',
      },
    });
    if (res.data && res.data.length > 0) {
      const { lat, lon } = res.data[0];
      setPosition([parseFloat(lat), parseFloat(lon)]);
      setFlyToRequested(true); // mark flyTo as needed
    }
  };

  

  const handleCreated = (e: any) => {
  const layer = e.layer;
  if (layer instanceof L.Polygon) {
    const latlngs = layer.getLatLngs()[0].map((pt: any) => [pt.lng, pt.lat]); // Turf uses [lng, lat]
    const polygon = turf.polygon([[...latlngs, latlngs[0]]]); // Close the polygon
    const area = turf.area(polygon); // in m²

    const platznummer = prompt("Platznummer:");
    const beschreibung = prompt("Beschreibung:");

    setStellplaetze(prev => [
      ...prev,
      {
        coordinates: latlngs.map(([lng, lat]: [number, number]) => [lat, lng]),
        platznummer: platznummer || "",
        beschreibung: beschreibung || "",
        groesse: Math.round(area),
      }
    ]);
  }
};

  const handleSave = () => {
    console.log("Neuer Campingplatz:", {
      address,
      position,
      stellplaetze,
    });

    // Optional: POST to your backend
  };

  const handleFieldChange = (index: number, field: string, value: any) => {
    setStellplaetze((prev) =>
      prev.map((platz, idx) =>
        idx === index ? { ...platz, [field]: value } : platz
      )
    );
  };

  const FeatureGroupWrapper = forwardRef(({ onCreated }: any, ref: any) => (
    <FeatureGroup ref={ref}>
      <EditControl
        position="topright"
        onCreated={onCreated}
        onEditStart={handleEditStart}
        draw={{
          rectangle: false,
          circle: false,
          polyline: false,
          circlemarker: false,
          marker: false,
          polygon: true,
        }}
      />
    </FeatureGroup>
  ));

const handleEditStart = () => {
  const layers = drawnItemsRef.current?.getLayers?.();
  if (layers && layers.length > 0) {
    const layer = layers[0]; // ggf. erweitern für mehrere Polygone
    layer.openPopup();
  }
};

  
  return (
    <div>
      <h2>Campingplatz anlegen</h2>
      <input
        type="text"
        value={address}
        onChange={(e) => setAddress(e.target.value)}
        placeholder="Adresse eingeben"
      />
      <button onClick={handleSearch}>Adresse suchen</button>

      <div style={{ height: '500px', marginTop: '1rem' }}>
        <MapContainer center={[47.0, 13.0]} zoom={18} maxZoom={20} style={{ height: '100%', width: '100%' }}>
          {/* <TileLayer
            attribution='&copy; OpenStreetMap contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          /> */}
          <TileLayer
            url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
            attribution="© Esri"
            maxZoom={20}
          />
          {position && (
            <>
              <Marker position={position} />
              <FlyToAddress position={position} flyToRequested={flyToRequested} setFlyToRequested={setFlyToRequested} />
            </>
          )}
          <FeatureGroupWrapper onCreated={handleCreated} ref={drawnItemsRef} />
          {stellplaetze.map((platz, idx) => (
            <Polygon key={idx} positions={platz.coordinates} color="blue">
              <Popup>
                {editingIndex === idx ? (
                  <div>
                    <label>
                      Platznummer:
                      <input
                        type="text"
                        value={platz.platznummer}
                        onChange={(e) => handleFieldChange(idx, 'platznummer', e.target.value)}
                      />
                    </label><br />
                    <label>
                      Größe (m²):
                      <input
                        type="number"
                        value={platz.groesse}
                        onChange={(e) => handleFieldChange(idx, 'groesse', Number(e.target.value))}
                      />
                    </label><br />
                    <label>
                      Beschreibung:
                      <textarea
                        value={platz.beschreibung}
                        onChange={(e) => handleFieldChange(idx, 'beschreibung', e.target.value)}
                      />
                    </label><br />
                    <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '0.5rem' }}>
                      <Button
                        variant="contained"
                        color="secondary"
                        onClick={() => setEditingIndex(null)}
                        size="small"
                      >Abbrechen</Button>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => setEditingIndex(null)}
                        size="small"
                      >Speichern</Button>
                    </div>
                  </div>
                ) : (
                  <div style={{ width: '160px',position: 'relative', paddingTop: '0.5rem' }}>
                    <FaEdit onClick={() => setEditingIndex(idx)} style={{
                      position: 'absolute',
                      top: '0.5rem',
                      right: '0.5rem',
                      cursor: 'pointer',
                    }}></FaEdit>
                      <div>
                      <strong>Platz {platz.platznummer} - {platz.groesse} m²</strong><br />
                      <i>{platz.beschreibung}</i><br />
                    </div>
                  </div>
                )}
              </Popup>
            </Polygon>
          ))}

        </MapContainer>
      </div>

      <button style={{ marginTop: '1rem' }} onClick={handleSave}>Speichern</button>
    </div>
  );
}


