import React, { useState } from 'react';
import axios from 'axios';
import { HeaderMenu } from '../Components/HeaderMenu';
import { AppShell, Burger, Center, Flex, Paper } from '@mantine/core';
import { useDisclosure, useScrollIntoView } from '@mantine/hooks';
        

function Home() {
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [opened, { toggle }] = useDisclosure();
  const { scrollIntoView: scrollToFunctions, targetRef: functionsRef } = useScrollIntoView<HTMLDivElement>({offset: 60,});
  const { scrollIntoView: scrollToAbout, targetRef: aboutRef } = useScrollIntoView<HTMLDivElement>({offset: 60,});
  const { scrollIntoView: scrollToPricing, targetRef: pricingRef } = useScrollIntoView<HTMLDivElement>({offset: 60,});

  const handleRegister = async () => {
    try {
      const response = await axios.post('/api/users/register', {
        email,
        username,
        password,
      });
      alert(response.data.message);
    } catch (error) {
      console.error('Error registering user:', error);
      alert('Failed to register user.');
    }
  };

  return (
      <AppShell
      padding={{ base: 10, sm: 15, lg: 'xl' }}
      header={{ 
        height: 56,
        collapsed: false,
        offset: true,
       }}
      navbar={{
        width: 300,
        breakpoint: 'md',
        collapsed: { mobile: !opened , desktop: true },
      }}
    >
      <AppShell.Header>
        <HeaderMenu scrollToFunctions={scrollToFunctions} scrollToAbout={scrollToAbout} scrollToPricing={scrollToPricing} />
      </AppShell.Header>
      <AppShell.Main>
        <h1>Welcome to CampGrid</h1>

        <h1 id='functions' ref={functionsRef}>Funktionen</h1>
        <p>Hier sind einige Funktionen von CampGrid:</p>
        <ul>
          <li>Benutzerfreundliche Oberfläche</li>
          <li>Einfaches Campingplatz-Management</li>
          <li>Reservierungssystem</li>
          <li>Verwaltung von Benutzern und Rollen</li>
          <li>Responsive Design für mobile Geräte</li>
          <li>Benutzerfreundliche Oberfläche</li>
          <li>Einfaches Campingplatz-Management</li>
          <li>Reservierungssystem</li>
          <li>Verwaltung von Benutzern und Rollen</li>
          <li>Responsive Design für mobile Geräte</li>
          <li>Benutzerfreundliche Oberfläche</li>
          <li>Einfaches Campingplatz-Management</li>
          <li>Reservierungssystem</li>
          <li>Verwaltung von Benutzern und Rollen</li>
          <li>Responsive Design für mobile Geräte</li>
          <li>Benutzerfreundliche Oberfläche</li>
          <li>Einfaches Campingplatz-Management</li>
          <li>Reservierungssystem</li>
          <li>Verwaltung von Benutzern und Rollen</li>
          <li>Responsive Design für mobile Geräte</li>
        </ul>
        <h1 id='about' ref={aboutRef}>Über uns</h1>
        <p>CampGrid ist eine Plattform, die es Nutzern ermöglicht, Campingplätze zu verwalten und zu reservieren. Unser Ziel ist es, das Camping-Erlebnis zu verbessern und die Verwaltung von Campingplätzen zu vereinfachen.</p>
        <p>Unser Team besteht aus erfahrenen Entwicklern und Camping-Enthusiasten, die eine Leidenschaft für Outdoor-Aktivitäten teilen.</p>
        <p>Wir glauben, dass Camping eine großartige Möglichkeit ist, die Natur zu genießen und Zeit mit Freunden und Familie zu verbringen.</p>
        <p>CampGrid ist eine Plattform, die es Nutzern ermöglicht, Campingplätze zu verwalten und zu reservieren. Unser Ziel ist es, das Camping-Erlebnis zu verbessern und die Verwaltung von Campingplätzen zu vereinfachen.</p>
        <p>Unser Team besteht aus erfahrenen Entwicklern und Camping-Enthusiasten, die eine Leidenschaft für Outdoor-Aktivitäten teilen.</p>
        <p>Wir glauben, dass Camping eine großartige Möglichkeit ist, die Natur zu genießen und Zeit mit Freunden und Familie zu verbringen.</p>
        <p>CampGrid ist eine Plattform, die es Nutzern ermöglicht, Campingplätze zu verwalten und zu reservieren. Unser Ziel ist es, das Camping-Erlebnis zu verbessern und die Verwaltung von Campingplätzen zu vereinfachen.</p>
        <p>Unser Team besteht aus erfahrenen Entwicklern und Camping-Enthusiasten, die eine Leidenschaft für Outdoor-Aktivitäten teilen.</p>
        <p>Wir glauben, dass Camping eine großartige Möglichkeit ist, die Natur zu genießen und Zeit mit Freunden und Familie zu verbringen.</p>
        <p>CampGrid ist eine Plattform, die es Nutzern ermöglicht, Campingplätze zu verwalten und zu reservieren. Unser Ziel ist es, das Camping-Erlebnis zu verbessern und die Verwaltung von Campingplätzen zu vereinfachen.</p>
        <p>Unser Team besteht aus erfahrenen Entwicklern und Camping-Enthusiasten, die eine Leidenschaft für Outdoor-Aktivitäten teilen.</p>
        <p>Wir glauben, dass Camping eine großartige Möglichkeit ist, die Natur zu genießen und Zeit mit Freunden und Familie zu verbringen.</p>

        <h1 id='pricing' ref={pricingRef}>Preise</h1>
        <p>Unsere Preisgestaltung ist transparent und fair. Wir bieten verschiedene Pläne an, die auf die Bedürfnisse von Einzelpersonen und Unternehmen zugeschnitten sind.</p>
        <p>Unsere Preisgestaltung ist transparent und fair. Wir bieten verschiedene Pläne an, die auf die Bedürfnisse von Einzelpersonen und Unternehmen zugeschnitten sind.</p>
        <p>Unsere Preisgestaltung ist transparent und fair. Wir bieten verschiedene Pläne an, die auf die Bedürfnisse von Einzelpersonen und Unternehmen zugeschnitten sind.</p>
        <p>Unsere Preisgestaltung ist transparent und fair. Wir bieten verschiedene Pläne an, die auf die Bedürfnisse von Einzelpersonen und Unternehmen zugeschnitten sind.</p>
        <p>Unsere Preisgestaltung ist transparent und fair. Wir bieten verschiedene Pläne an, die auf die Bedürfnisse von Einzelpersonen und Unternehmen zugeschnitten sind.</p>
        <p>Unsere Preisgestaltung ist transparent und fair. Wir bieten verschiedene Pläne an, die auf die Bedürfnisse von Einzelpersonen und Unternehmen zugeschnitten sind.</p>
      </AppShell.Main>
      </AppShell>
  );
}

export default Home;