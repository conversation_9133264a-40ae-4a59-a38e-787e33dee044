import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { HeaderMenu } from '../Components/HeaderMenu';

interface User {
    email: string;
    username: string;
    role: string;
}

const UserDashboard = () => {
    const [campsites, setCampsites] = useState<{ id: string; email: string; name: string }[]>([]);
    const API_URL = import.meta.env.VITE_API_URL;
    
    useEffect(() => {
        // Fetch campsites from the backend
        axios.get(`${API_URL}/api/campsites`)
            .then((response) => {
                console.log('Fetched campsites:', response.data); // Debugging log
                setCampsites(response.data);
            })
            .catch((error) => {
                console.error('Error fetching campsites:', error);
            });
    }, []);

    return (
        <div>
            <HeaderMenu />
            <h1>User Dashboard</h1>
            <h2>List of Campsites</h2>
            <ul>
                {campsites.map(campsite => (
                    <li key={campsite.id}>{campsite.name} - Operator: {campsite.email}</li>
                ))}
            </ul>
        </div>
    );
};

export default UserDashboard;