import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface User {
    email: string;
    username: string;
    role: string;
}

const OperatorDashboard = () => {
    const [users, setUsers] = useState<User[]>([]);
    const [campsites, setCampsites] = useState<{ id: string; email: string; name: string }[]>([]);
    const API_URL = import.meta.env.VITE_API_URL;

    useEffect(() => {
        // Fetch users from the backend
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('No token found');
            return;
        }

        axios.get<User[]>(`${API_URL}/api/users`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
            .then((response: { data: User[] }) => {
                console.log('Fetched users:', response.data); // Debugging log
                setUsers(response.data);
            })
            .catch((error: any) => {
                console.error('Error fetching users:', error);
                console.error('Error details:', error.response?.data);
            });
    }, []);

    useEffect(() => {
        // Fetch campsites from the backend
        axios.get(`${API_URL}/api/campsites`)
            .then((response) => {
                console.log('Fetched campsites:', response.data); // Debugging log
                setCampsites(response.data);
            })
            .catch((error) => {
                console.error('Error fetching campsites:', error);
            });
    }, []);

    return (
        <div>
            <h1>Admin Dashboard</h1>
            <h2>List of Users</h2>
            <ul>
                {users.map(user => (
                    <li key={user.email}>{user.username} ({user.email}) - Role: {user.role}</li>
                ))}
            </ul>
            <h2>List of Campsites</h2>
            <ul>
                {campsites.map(campsite => (
                    <li key={campsite.id}>{campsite.name} - Operator: {campsite.email}</li>
                ))}
            </ul>
        </div>
    );
};

export default OperatorDashboard;