import { BrowserRouter, Routes, Route } from 'react-router-dom';
import AdminDashboard from './pages/AdminDashboard';
import RegisterCampsite from './pages/RegisterCampsite';
import Home from './pages/Home';
import RegisterUser from './pages/RegisterUser';
import ProtectedRoute from './pages/ProtectedRoute';
import UserDashboard from './pages/UserDashboard';
import Campsite from './pages/Campsite';
import CreateCampground from './pages/CreateCampground';

import '@mantine/core/styles.css';
import { MantineProvider } from '@mantine/core';
// import './css/campgrid-theme.css';
import { customTheme } from './theme';


function App() {
  return (
    <MantineProvider theme={customTheme}>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/register-campsite" element={<RegisterCampsite />} />
          <Route path="/register-user" element={<RegisterUser />} />
          <Route path="/admin/dashboard" element={<ProtectedRoute role="admin"> <AdminDashboard /> </ProtectedRoute>}/>
          <Route path="/user/dashboard" element={<ProtectedRoute role="user"> <UserDashboard /> </ProtectedRoute>}/>
          <Route path="/campsites/:campsite_id" element={<Campsite />} />
          <Route path="/create" element={<CreateCampground />} />
        </Routes>
      </BrowserRouter>
    </MantineProvider>
  );
}

export default App;
