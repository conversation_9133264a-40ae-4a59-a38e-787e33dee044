import { useForm } from '@mantine/form';
import { NumberInput, TextInput, Button, Group, Stack, useRandomClassName, PasswordInput } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import { use, useState } from 'react';

import classes from '../css/Login.module.css';

function LoginForm(props: any) {
  const navigate = useNavigate();
  const [shake, setShake] = useState(false);
  
  const form = useForm({
    mode: 'uncontrolled',
    initialValues: { email: '', password: '' },

    transformValues: (values) => ({
      email: `${values.email}`,
      password: `${values.password}`,
    }),
  });

  const handleRegisterClick = () => {
    props.openRegisterModal();
    props.closeLoginModal();
  };

  const handleLogin = async (values: typeof form.values) => {
    console.log("Login values:", values);

    try {
      const API_URL = import.meta.env.VITE_API_URL;
      const req = {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values)
      };

      console.log("Login request:", req);

      const res = await fetch(`${API_URL}/token`, req);

      console.log("Login response:", res);
      
      if (!res.ok) {
        // Handle different error statuses
        if (res.status === 401) {
          triggerShake();
          // alert("Ungültige Anmeldedaten");
          return;
        } else if (res.status === 422) {
          alert("Ungültige Eingabedaten");
          return;
        } else {
          alert("Anmeldefehler aufgetreten");
          return;
        }
      }
      
      const data = await res.json();
      
      if (!data.access_token) {
        alert("Kein Zugriffstoken erhalten");
        return;
      }
      
      const payload = JSON.parse(atob(data.access_token.split(".")[1]));
      localStorage.setItem("token", data.access_token);
      navigate(`/${payload.role}/dashboard`);
      
    } catch (error) {
      console.error("Login error:", error);
      alert(error.message);
    }
  };

  const triggerShake = () => {
    setShake(true);
    form.setFieldError('email', ' ');
    form.setFieldError('password', 'Ungültige Anmeldedaten');
    setTimeout(() => setShake(false), 500);
  };

  return (
    <form onSubmit={form.onSubmit(handleLogin)}>
      <TextInput className={shake ? classes.shake : ''}
        label="Email"
        labelProps={{fz: 'xs'}}
        placeholder="Email"
        key={form.key('email')}
        {...form.getInputProps('email')}
      />
      <PasswordInput className={shake ? classes.shake : ''}
        label="Passwort"
        labelProps={{fz: 'xs'}}
        mt='xs'
        placeholder="Passwort"
        key={form.key('password')}
        {...form.getInputProps('password')}
      />
      <Stack justify="space-between" mt="md">
        <Button type="submit">
            Anmelden
        </Button>
        <Button type="button" onClick={handleRegisterClick} variant="outline">
            Registrieren
        </Button>
      </Stack>
    </form>
  );
}

export default LoginForm;