import { useForm } from '@mantine/form';
import { NumberInput, TextInput, Button, Group, Stack, useRandomClassName, PasswordInput, Checkbox } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import { use, useState } from 'react';

import classes from '../css/Login.module.css';

function RegisterUserForm(props: any) {
  const navigate = useNavigate();
  const [shake, setShake] = useState(false);
  
  const form = useForm({
    mode: 'uncontrolled',
    initialValues: { firstName: '', lastName: '', email: '', password: '', confirmPassword: '', terms: false },

    validate: {
      firstName: (value) => (value.length < 2 ? 'Vorname muss mindestens 2 Zeichen lang sein' : null),
      lastName: (value) => (value.length < 2 ? 'Nachname muss mindestens 2 Zeichen lang sein' : null),
      email: (value) => (/^\S+@\S+$/.test(value) ? null : 'Ungültige E-Mail-Adresse'),
      password: (value) => {
        if (value.length < 8) {
          return 'Passwort muss mindestens 8 Zeichen lang sein';
        }
        if (!/[0-9]/.test(value)) {
          return 'Passwort muss mindestens eine Zahl enthalten';
        }
        if (!/[!@#$%^&*(),.?":{}|<>_\-+=~`[\]\\\/;']/g.test(value)) {
          return 'Passwort muss mindestens ein Sonderzeichen enthalten';
        }
        return null;
      },
      confirmPassword: (value, values) => {
        if (value !== values.password) {
          return 'Passwort stimmt nicht überein';
        }
        return null;
      },
    },

    transformValues: (values) => ({
      firstName: `${values.firstName}`,
      lastName: `${values.lastName}`,
      email: `${values.email}`,
      password: `${values.password}`,
    }),
  });

  const handleRegister = async (values: typeof form.values) => {
    console.log("Register values:", values);

    try {
      const API_URL = import.meta.env.VITE_API_URL;
      const req = {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify( values )
      };

      console.log("Register request:", req);

      const res = await fetch(`${API_URL}/api/v1/auth/signup`, req);

      console.log("Register response:", res);

      if (!res.ok) {
        // Handle different error statuses
        if (res.status === 401) {
          // triggerShake();
          // alert("Ungültige Anmeldedaten");
          return;
        } else if (res.status === 422) {
          alert("Ungültige Eingabedaten");
          return;
        } else {
          alert("Anmeldefehler aufgetreten");
          return;
        }
      }
      
      const data = await res.json();

      console.log("Register response data:", data);

      if (data.status == 'error') {
        console.error('API error:', data.message);
        return;
      }

      if (!data.access_token) {
        alert("Kein Zugriffstoken erhalten");
        return;
      }
      
      const payload = JSON.parse(atob(data.access_token.split(".")[1]));
      localStorage.setItem("token", data.access_token);
      navigate(`/${payload.role}/dashboard`);
      
    } catch (error) {
      console.error("Login error:", error);
      alert(error.message);
    }
  };

  const triggerShake = () => {
    setShake(true);
    form.setFieldError('email', ' ');
    form.setFieldError('password', 'Ungültige Anmeldedaten');
    setTimeout(() => setShake(false), 500);
  };

  return (
    <form onSubmit={form.onSubmit(handleRegister)}>
      <TextInput 
        label="Vorname"
        labelProps={{fz: 'xs', required: true}}
        placeholder="Vorname"
        autoComplete='given-name'
        key={form.key('firstName')}
        {...form.getInputProps('firstName')}
      />
      <TextInput 
        label="Nachname"
        labelProps={{fz: 'xs', required: true}}
        mt={'xs'}
        placeholder="Nachname"
        autoComplete='family-name'
        key={form.key('lastName')}
        {...form.getInputProps('lastName')}
      />
      <TextInput 
        label="Email"
        labelProps={{fz: 'xs', required: true}}
        mt={'xs'}
        placeholder="Email"
        autoComplete='email'
        key={form.key('email')}
        {...form.getInputProps('email')}
      />
      <PasswordInput 
        label="Passwort"
        labelProps={{fz: 'xs', required: true}}
        mt={'xs'}
        placeholder="Passwort"
        autoComplete='password'
        key={form.key('password')}
        {...form.getInputProps('password')}
      />
      <PasswordInput 
        label="Passwort bestätigen"
        labelProps={{fz: 'xs', required: true}}
        mt={'xs'}
        placeholder="Passwort bestätigen"
        autoComplete='password'
        key={form.key('confirmPassword')}
        {...form.getInputProps('confirmPassword')}
      />
      <Checkbox 
        label="Ich stimme den Nutzungsbedingungen zu"
        mt='lg'
        required
        key={form.key('terms')}
        {...form.getInputProps('terms', { type: 'checkbox' })}
      />
      <Stack justify="space-between" mt="md">
        <Button type="submit" mt={'md'}>
            Registrieren
        </Button>
      </Stack>
    </form>
  );
}

export default RegisterUserForm;