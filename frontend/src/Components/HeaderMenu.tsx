import { Burger, Center, Container, Group, Menu, Modal } from '@mantine/core';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import CampgridLogo from '../assets/campgrid1.png';

import classes from '../css/HeaderMenu.module.css'; // Assuming you have a CSS module for styles

import { Drawer } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import Login from './Login';
import LoginForm from './LoginForm';
import { useMatch } from 'react-router-dom';
import { DefaultMenu } from './menu/DefaultMenu';
import { UserMenu } from './menu/UserMenu';
import RegisterUser from '../pages/RegisterUser';
import RegisterUserForm from './RegisterUserForm';


export function HeaderMenu(props: any) {
  const [menuOpened, { toggle }] = useDisclosure(false);
  const [loginModalOpened, { open: openLoginModal, close: closeLoginModal }] = useDisclosure(false);
  const [registerModalOpened, { open: openRegisterModal, close: closeRegisterModal }] = useDisclosure(false);
  const navigate = useNavigate();
  const isMobile = useMediaQuery('(max-width: 1068px)');


  const isAdminRoute = useMatch('/admin/*');
  const isOperatorRoute = useMatch('/operator/*');
  const isUserRoute = useMatch('/user/*');


  let menu: any;

  if (isAdminRoute) {
    menu = <DefaultMenu {...props} drawer={menuOpened} closeDrawer={toggle} classes={classes} openLoginModal={openLoginModal} openRegisterModal={openRegisterModal} />;
  } else if (isOperatorRoute) {
    menu = <DefaultMenu {...props} drawer={menuOpened} closeDrawer={toggle} classes={classes} openLoginModal={openLoginModal} openRegisterModal={openRegisterModal} />;
  } else if (isUserRoute) {
    menu = <UserMenu {...props} drawer={menuOpened} closeDrawer={toggle} classes={classes} openLoginModal={openLoginModal} />;
  } else {
    menu = <DefaultMenu {...props} drawer={menuOpened} closeDrawer={toggle} classes={classes} openLoginModal={openLoginModal} openRegisterModal={openRegisterModal} />;
  }

  return (
    <header className={classes.header}>
      <Container size="xxl">
        <div className={classes.inner}>
          <Group className={classes.headerGroup} justify='flex-start' onClick={() => navigate('/')} >
          <img src={CampgridLogo} alt="Campgrid Logo" height={40}/>
          <h1 className={classes.title}>CampGrid</h1>
          </Group>
          <Group gap={5} visibleFrom="sm">
            {menu}
          </Group>
          <Burger opened={menuOpened} onClick={toggle} size="sm" hiddenFrom="sm" />
          <Drawer opened={menuOpened} onClose={toggle} position='right' title="CampGrid">
            {menu}
          </Drawer>
          <Modal  opened={loginModalOpened} 
                  onClose={closeLoginModal} 
                  title="Anmelden"
                  size="sm"
                  >
            {<LoginForm openRegisterModal={openRegisterModal} closeLoginModal={closeLoginModal} />}
          </Modal>
          <Modal  opened={registerModalOpened} 
                  onClose={closeRegisterModal} 
                  title="Neues Konto erstellen"
                  size="md"
                  >
            {<RegisterUserForm />}
          </Modal>
        </div>
      </Container>
    </header>
  );
}