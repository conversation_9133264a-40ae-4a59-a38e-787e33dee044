import { Group, Flex, Avatar } from '@mantine/core';
import { useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';

export function UserMenu(props: any) {
  const navigate = useNavigate();
  const [active, setActive] = useState(0);

  return (
    <Flex className={props.classes.link} direction={props.drawer ? "column" : "row"}>
      <NavLink to="/create" className={props.classes.link}>Campingplatz anlegen</NavLink>
      <NavLink to="/user/dashboard" className={props.classes.link} >
        <Avatar radius="xl" size="md" />
      </NavLink>
    </Flex>
  );
}