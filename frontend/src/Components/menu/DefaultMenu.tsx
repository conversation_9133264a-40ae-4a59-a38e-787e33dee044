import { Group, Flex, NavLink } from '@mantine/core';
import { useMediaQuery, useScrollIntoView } from '@mantine/hooks';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';


export function DefaultMenu(props: any) {
  const navigate = useNavigate();
  const [active, setActive] = useState(0);
  const isMobile = useMediaQuery('(max-width: 1068px)');
  // const fontSizes = { base: 'xl', sm: 'md'};


  const handleFunctionsClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    props.scrollToFunctions();
    if (props.drawer) props.closeDrawer();
  };

  const handleAboutClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    props.scrollToAbout();
    if (props.drawer) props.closeDrawer();
  };

  const handlePricingClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    props.scrollToPricing();
    if (props.drawer) props.closeDrawer();
  };

  const handleRegisterClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    props.openRegisterModal();
    if (props.drawer) props.closeDrawer();
  };

  const handleLoginClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    props.openLoginModal();
    if (props.drawer) props.closeDrawer();
  };

  return (
    <Flex className={props.classes.link} direction={props.drawer ? "column" : "row"} >
      <NavLink href="#functions" onClick={handleFunctionsClick} label="Funktionen" noWrap className={props.classes.link} />
      <NavLink href="#about" onClick={handleAboutClick} label="Über uns" noWrap className={props.classes.link} />
      <NavLink href="#pricing" onClick={handlePricingClick} label="Preise" noWrap className={props.classes.link} />
      {/* <NavLink href="#register" onClick={handleRegisterClick} label="Registrieren" noWrap className={props.classes.link} /> */}
      <NavLink href="#login" onClick={handleLoginClick} label="Anmelden" noWrap className={props.classes.link} />
    </Flex>
  );
}