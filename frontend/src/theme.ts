import { ThemeContext } from '@emotion/react';
import { Button, MantineThemeOverride, Modal, Stack } from '@mantine/core';
import { colorResolver } from '@mantine/core/lib/core/Box/style-props/resolvers/color-resolver/color-resolver';
import { NavLink } from 'react-router-dom';

export const customTheme: MantineThemeOverride = {
  focusRing: 'auto',
  colors: {
    brand: [
      '#2E5339', '#3A6B4D', '#4A8B61', '#5AA576', '#6ABF8B',
      '#7ADFA0', '#8AF1B5', '#9AF3C9', '#AAF5DD', '#BAF7F1',
    ],
    secondary: [
      '#2e5339', '#506f4c', '#728b5a', '#919d65', '#aca86b',
      '#c4b16c', '#d8ae68', '#e9a360', '#f69853', '#ff8c42',
    ],
  },
  cursorType: 'pointer',
  primaryColor: 'brand',
  primaryShade: 3,

  fontFamily: 'Inter, sans-serif',
  headings: { fontFamily: 'Inter, sans-serif' },

  defaultRadius: 'md',

  components: {
    Button: {
      defaultProps: {
        color: 'brand',
        variant: 'filled',
        size: 'sm',
        radius: 'lg',
      },
    },
    Modal: {
      defaultProps: {
        centered: false,
        size: { base: 'xl', sm: 'lg', md: 'sm' },
        radius: 'md',
      },
    },
    NavLink: {
      defaultProps: {
        color: 'brand',
        variant: 'subtle',
        size: 'sm',
        radius: 'md',
        style: { textDecoration: 'none' },
      },
    },
  },
};
