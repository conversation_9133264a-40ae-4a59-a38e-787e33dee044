.header {
  height: 56px;
  margin-bottom: 20px;
  background-color: var(--mantine-color-body);
  border-bottom: 1px solid var(--mantine-color-brand-0);
}

.headerGroup {
  display: flex;
  align-items: center;
  font-size: var(--mantine-font-size-xs);
  font-family: var(--mantine-font-family);
  cursor: pointer;
}

.inner {
  height: 56px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.link {
  display: block;
  line-height: 1.5;
  padding: 8px 8px;
  border-radius: var(--mantine-radius-md);
  text-decoration: none;
  color: light-dark(var(--mantine-color-brand-0), var(--mantine-color-dark-0));
  align-content: center;
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;

  @mixin hover {
    /* background-color: light-dark(var(--mantine-color-brand-1), var(--mantine-color-dark-6)); */
    border: 5px solid light-dark(var(--mantine-color-brand-1), var(--mantine-color-dark-6));
  }
}


.title {
  font-size: var(--mantine-font-size-xl);
  font-weight: 700;
  color: var(--mantine-color-brand-0);
  height: auto;
  margin: 0 0;
}