/* === COLOR VARIABLES === */
:root {
  --cg-primary: #2e5339;        /* Dark green */
  --cg-secondary: #6aa84f;      /* Medium green */
  --cg-accent: #f9a825;         /* Warm yellow */
  --cg-bg-light: #f5f5f5;       /* Light background */
  --cg-text: #212121;           /* Dark text */
  --cg-muted: #757575;          /* Muted text */
  --cg-white: #ffffff;
}

/* === BASE TYPOGRAPHY === */
body {
  font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--cg-bg-light);
  color: var(--cg-text);
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

/* === HEADINGS === */
h1, h2, h3, h4, h5, h6 {
  color: var(--cg-primary);
  margin-top: 1em;
  font-weight: 600;
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

/* === LINKS === */
a {
  color: var(--cg-secondary);
  text-decoration: none;
}
a:hover {
  color: var(--cg-accent);
  text-decoration: underline;
}

/* === BUTTONS === */
button {
  background-color: var(--cg-primary);
  color: var(--cg-white);
  border: none;
  padding: 0.6em 1.2em;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
}
button:hover {
  background-color: var(--cg-secondary);
}

/* === FORMS === */
input, select, textarea {
  padding: 0.5em;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
}
input:focus, textarea:focus {
  outline: none;
  border-color: var(--cg-accent);
  box-shadow: 0 0 0 2px rgba(249, 168, 37, 0.2);
}

/* === CONTAINERS === */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* === CARDS === */
.card {
  background: var(--cg-white);
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

/* === UTILS === */
.text-muted {
  color: var(--cg-muted);
}

.bg-primary {
  background-color: var(--cg-primary);
  color: var(--cg-white);
}

.bg-accent {
  background-color: var(--cg-accent);
  color: var(--cg-text);
}
